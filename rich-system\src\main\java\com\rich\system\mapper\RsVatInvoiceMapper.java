package com.rich.system.mapper;

import java.util.List;
import java.util.Map;

import com.rich.common.core.domain.entity.RsVatInvoice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 发票登记Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Mapper
public interface RsVatInvoiceMapper {
    /**
     * 查询发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 发票登记
     */
    RsVatInvoice selectRsVatInvoiceByInvoiceId(Long invoiceId);

    /**
     * 查询发票登记列表
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记集合
     */
    List<RsVatInvoice> selectRsVatInvoiceList(RsVatInvoice rsVatInvoice);

    /**
     * 新增发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    int insertRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 修改发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    int updateRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 删除发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceId(Long invoiceId);

    /**
     * 批量删除发票登记
     *
     * @param invoiceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceIds(Long[] invoiceIds);

    /**
     * 根据rctId查询发票数量
     *
     * @param rctId RCT主键
     * @return 发票数量
     */
    int countRsVatInvoiceByRctId(Long rctId);

    /**
     * 根据rctId和cooperatorId查询发票数量
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @return 发票数量
     */
    int countRsVatInvoiceByRctIdAndCooperatorId(Long rctId, Long cooperatorId);

    /**
     * 一次性查询rctId总发票数和指定cooperatorId的发票数
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @return 包含两个计数的Map，键为"totalCount"和"cooperatorCount"
     */
    Map<String, Object> countBothInvoices(@Param("rctId") Long rctId, @Param("cooperatorId") Long cooperatorId);

    /**
     * 根据发票ID列表批量查询发票信息
     *
     * @param invoiceIds 发票ID列表
     * @return 发票信息列表
     */
    List<RsVatInvoice> selectRsVatInvoiceByInvoiceIds(@Param("invoiceIds") List<Long> invoiceIds);

    /**
     * 根据发票ID列表查询发票信息（包含已删除的）
     *
     * @param invoiceIds 发票ID列表
     * @return 发票列表
     */
    List<RsVatInvoice> selectRsVatInvoiceByInvoiceIdsIncludeDeleted(List<Long> invoiceIds);

    /**
     * 恢复被删除的发票
     *
     * @param invoiceId 发票ID
     * @return 结果
     */
    int restoreRsVatInvoiceByInvoiceId(Long invoiceId);

    /**
     * 查找已取消的发票编码列表
     * 用于发票号码重用，查找指定RCT和合作方下状态为"canceled"的发票编码
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @param firstLetter  发票编码首字母
     * @return 已取消的发票编码列表
     */
    List<String> findCancelledInvoiceCodes(@Param("rctId") Long rctId,
                                          @Param("cooperatorId") Long cooperatorId,
                                          @Param("firstLetter") String firstLetter);
}
