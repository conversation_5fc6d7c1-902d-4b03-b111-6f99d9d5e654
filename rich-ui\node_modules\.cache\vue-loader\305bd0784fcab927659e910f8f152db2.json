{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue?vue&type=template&id=42510a1d&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue", "mtime": 1756714775190}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}