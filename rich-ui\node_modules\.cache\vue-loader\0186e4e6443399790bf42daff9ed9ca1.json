{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue", "mtime": 1756778899811}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2dldENvbXBhbnl9IGZyb20gIkAvYXBpL3N5c3RlbS9jb21wYW55Ig0KaW1wb3J0IHtsaXN0QWNjb3VudH0gZnJvbSAiQC9hcGkvc3lzdGVtL2FjY291bnQiDQppbXBvcnQge3VwZGF0ZVZhdGludm9pY2V9IGZyb20gIkAvYXBpL3N5c3RlbS92YXRJbnZvaWNlIg0KaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiDQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSINCmltcG9ydCB7dXBkYXRlRGViaXROb3RlQnlJbnZvaWNlSWR9IGZyb20gIkAvYXBpL3N5c3RlbS9kZWJpdG5vdGUiDQoNCi8vIOmYsuaKluWHveaVsA0KZnVuY3Rpb24gZGVib3VuY2UoZm4sIGRlbGF5KSB7DQogIGxldCB0aW1lciA9IG51bGwNCiAgcmV0dXJuIGZ1bmN0aW9uICgpIHsNCiAgICBjb25zdCBjb250ZXh0ID0gdGhpcw0KICAgIGNvbnN0IGFyZ3MgPSBhcmd1bWVudHMNCiAgICBjbGVhclRpbWVvdXQodGltZXIpDQogICAgdGltZXIgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgIGZuLmFwcGx5KGNvbnRleHQsIGFyZ3MpDQogICAgfSwgZGVsYXkpDQogIH0NCn0NCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVmF0aW52b2ljZURpYWxvZyIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBGaWxlVXBsb2FkLA0KICAgIFRyZWVzZWxlY3QNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICAvLyDmmK/lkKbmmL7npLrlr7nor53moYYNCiAgICB2aXNpYmxlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIC8vIOagh+mimA0KICAgIHRpdGxlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAiIg0KICAgIH0sDQogICAgLy8g6KGo5Y2V5pWw5o2uDQogICAgZm9ybTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgcnVsZXM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIC8vIOWPkeelqOaYjue7huWIl+ihqA0KICAgIGludm9pY2VJdGVtczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgY29tcGFueUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIC8vIOmTtuihjOi0puaIt+WIl+ihqA0KICAgIGJhbmtBY2NvdW50TGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgdHlwZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogIiINCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOWGhemDqOWvueivneahhuWPr+ingeaAp+eKtuaAgQ0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDooajljZXmlbDmja7nmoTlia/mnKwNCiAgICAgIGZvcm1EYXRhOiB7fSwNCiAgICAgIC8vIOWPkeelqOaYjue7huWIl+ihqOeahOWJr+acrA0KICAgICAgaW52b2ljZUl0ZW1MaXN0OiBbXSwNCiAgICAgIC8vIOmYsuaKluWQjueahOiOt+WPluWFrOWPuOS/oeaBr+aWueazlQ0KICAgICAgZGVib3VuY2VkRmV0Y2hDb21wYW55SW5mbzogbnVsbCwNCiAgICAgIC8vIOWFrOWPuOmTtuihjOi0puaIt+WIl+ihqA0KICAgICAgY29tcGFueUJhbmtMaXN0OiBbXSwNCiAgICAgIC8vIOW8gOelqOmhueebrumAiemhueaVsOaNrg0KICAgICAgaW52b2ljaW5nSXRlbU9wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAiMSIsDQogICAgICAgICAgaW52b2ljaW5nSXRlbU5hbWU6ICLnu4/nuqrku6PnkIbmnI3liqEiLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7aWQ6ICIxLTEiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhui/kOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbmnI3liqHotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMyIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5riv5p2C6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTQiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuWbvemZhei0p+eJqei/kOi+k+S7o+eQhuacjeWKoSJ9LA0KICAgICAgICAgICAge2lkOiAiMS01IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbmiqXlhbPmnI3liqHotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtNiIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5pyN5Yqh6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTciLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuaKpeWFs+i0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS04IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbmi5bovabotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtOSIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Zu96ZmF6LSn54mp6L+Q6L6T5Luj55CG5pyN5YqhLeS7o+eQhui/kOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xMCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5Zu95YaF6L+Q6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTExIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbmtbfov5DotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTMiLCBpbnZvaWNpbmdJdGVtTmFtZTogIui/kOi+k+S7o+eQhui0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xNCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi6LSn54mp6L+Q6L6T5Luj55CG5pyN5Yqh6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTE1IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbmuK/mnYLotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTYiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuWbvemZhei0p+eJqei/kOi+k+S7o+eQhui/kOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xNyIsIGludm9pY2luZ0l0ZW1OYW1lOiAi6LSn54mp6L+Q6L6T5Luj55CG6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTE4IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTkiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuadgui0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yMCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5paH5Lu26LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTIxIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIborr7lpIfkuqTmjqXljZXotLnnlKgifSwNCiAgICAgICAgICAgIHtpZDogIjEtMjIiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuiIseWNleeUs+aKpei0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yMyIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5pON5L2c6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTI0IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIblsIHmnaHotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMjUiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhueggeWktOaTjeS9nOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yNiIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG55S15pS+6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTI3IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbmoLjph43otLkifQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOaJuemHj+mAieaLqeebuOWFs+aVsOaNrg0KICAgICAgc2VsZWN0ZWRSb3dzOiBbXSwgLy8g6YCJ5Lit55qE6KGM5pWw5o2uDQogICAgICBiYXRjaEludm9pY2luZ0l0ZW1EaWFsb2dWaXNpYmxlOiBmYWxzZSwgLy8g5om56YeP6K6+572u5byA56Wo6aG555uu5a+56K+d5qGG5Y+v6KeB5oCnDQogICAgICBiYXRjaEZvcm06IHsNCiAgICAgICAgaW52b2ljaW5nSXRlbTogbnVsbCAvLyDmibnph4/orr7nva7nmoTlvIDnpajpobnnm64NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5Y+v55So55qE6ZO26KGM6LSm5oi35YiX6KGo77ya5LyY5YWI5L2/55SoY29tcGFueUJhbmtMaXN077yM5Li656m65pe25L2/55So5Lyg5YWl55qEYmFua0FjY291bnRMaXN0DQogICAgYXZhaWxhYmxlQmFua0xpc3QoKSB7DQogICAgICByZXR1cm4gdGhpcy5jb21wYW55QmFua0xpc3QubGVuZ3RoID4gMCA/IHRoaXMuY29tcGFueUJhbmtMaXN0IDogdGhpcy5iYW5rQWNjb3VudExpc3QNCiAgICB9DQogIH0sDQoNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDliJvlu7rpmLLmipbniYjmnKznmoRmZXRjaENvbXBhbnlJbmZv5pa55rOV77yM6K6+572uMzAwbXPlu7bov58NCiAgICB0aGlzLmRlYm91bmNlZEZldGNoQ29tcGFueUluZm8gPSBkZWJvdW5jZSh0aGlzLmZldGNoQ29tcGFueUluZm8sIDMwMCkNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2aXNpYmxlOiB7DQogICAgICBoYW5kbGVyKHZhbCkgew0KICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB2YWwNCiAgICAgICAgaWYgKHZhbCkgew0KICAgICAgICAgIC8vIOW9k+WvueivneahhuaYvuekuuaXtu+8jOWkjeWItuS8oOWFpeeahOaVsOaNrg0KICAgICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybSkpDQogICAgICAgICAgdGhpcy5pbnZvaWNlSXRlbUxpc3QgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuaW52b2ljZUl0ZW1zKSkNCg0KICAgICAgICAgIC8vIOehruS/neWPkeelqOmZhOS7tuWtl+auteWtmOWcqA0KICAgICAgICAgIGlmICghdGhpcy5mb3JtRGF0YS5pbnZvaWNlQXR0YWNobWVudCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtRGF0YS5pbnZvaWNlQXR0YWNobWVudCA9ICIiDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5aaC5p6c5omA5bGe5YWs5Y+45bey5pyJ5YC877yM6Ieq5Yqo5aGr5YWF55u45YWz5L+h5oGvDQogICAgICAgICAgaWYgKHRoaXMuZm9ybURhdGEuaW52b2ljZUJlbG9uZ3NUbykgew0KICAgICAgICAgICAgdGhpcy5hdXRvRmlsbENvbXBhbnlJbmZvKHRoaXMuZm9ybURhdGEuaW52b2ljZUJlbG9uZ3NUbykNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDliJ3lp4vljJbml7borqHnrpflj5Hnpajph5Hpop0NCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmNhbGN1bGF0ZUludm9pY2VBbW91bnQoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9LA0KICAgIC8vIOebkeWQrCBmb3JtIHByb3Ag5Y+Y5YyW77yM5b2T54i257uE5Lu25pu05paw5Y+R56Wo5pWw5o2u5pe26Ieq5Yqo5ZCM5q2l5Yiw5a2Q57uE5Lu2DQogICAgZm9ybTogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCAmJiBPYmplY3Qua2V5cyhuZXdWYWwpLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDlvZMgZm9ybSBwcm9wIOWPmOWMluaXtu+8jOabtOaWsOWGhemDqOeahCBmb3JtRGF0YQ0KICAgICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG5ld1ZhbCkpDQoNCiAgICAgICAgICAvLyDlpoLmnpzmiYDlsZ7lhazlj7jlt7LmnInlgLzvvIzoh6rliqjloavlhYXnm7jlhbPkv6Hmga8NCiAgICAgICAgICBpZiAodGhpcy5mb3JtRGF0YS5pbnZvaWNlQmVsb25nc1RvKSB7DQogICAgICAgICAgICB0aGlzLmF1dG9GaWxsQ29tcGFueUluZm8odGhpcy5mb3JtRGF0YS5pbnZvaWNlQmVsb25nc1RvKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmHjeaWsOiuoeeul+WPkeelqOmHkeminQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuY2FsY3VsYXRlSW52b2ljZUFtb3VudCgpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9LA0KICAgIC8vIOebkeWQrCBpbnZvaWNlSXRlbXMgcHJvcCDlj5jljJbvvIzlvZPniLbnu4Tku7bmm7TmlrDotLnnlKjmmI7nu4bml7boh6rliqjlkIzmraXliLDlrZDnu4Tku7YNCiAgICAvLyBpbnZvaWNlSXRlbXM6IHsNCiAgICAvLyAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgLy8gICAgIGlmIChuZXdWYWwgJiYgQXJyYXkuaXNBcnJheShuZXdWYWwpKSB7DQogICAgLy8gICAgICAgLy8g5b2TIGludm9pY2VJdGVtcyBwcm9wIOWPmOWMluaXtu+8jOabtOaWsOWGhemDqOeahCBpbnZvaWNlSXRlbUxpc3QNCiAgICAvLyAgICAgICB0aGlzLmludm9pY2VJdGVtTGlzdCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobmV3VmFsKSkNCg0KICAgIC8vICAgICAgIC8vIOmHjeaWsOiuoeeul+WPkeelqOmHkeminQ0KICAgIC8vICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAvLyAgICAgICAgIHRoaXMuY2FsY3VsYXRlSW52b2ljZUFtb3VudCgpDQogICAgLy8gICAgICAgfSkNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSwNCiAgICAvLyAgIGRlZXA6IHRydWUNCiAgICAvLyB9LA0KICAgIC8vIOebkeWQrOWvueaWueWFrOWPuElE5Y+Y5YyWDQogICAgImZvcm1EYXRhLmNvb3BlcmF0b3JJZCI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgLy8g5Y+q5pyJ5b2T5YC855yf5q2j5Y+Y5YyW5pe25omN6Kem5Y+R5p+l6K+iDQogICAgICAgIHRoaXMuZGVib3VuY2VkRmV0Y2hDb21wYW55SW5mbyhuZXdWYWwpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDnm5HlkKzmiYDlsZ7lhazlj7jlj5jljJbvvIzoh6rliqjloavlhYXmiJHlj7jlj5HnpajmiqzlpLTlkoznqI7lj7cNCiAgICAiZm9ybURhdGEuaW52b2ljZUJlbG9uZ3NUbyI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgICB0aGlzLmF1dG9GaWxsQ29tcGFueUluZm8obmV3VmFsKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDlj5Hnpajph5Hpop3oh6rliqjmoLnmja7lj5HnpajmsYfnjocv5Y+R56Wo5biB56eNL+WPkeelqOaYjue7huihqOeahOWQq+eojuWwj+iuoeiuoeeulw0KICAgICJmb3JtRGF0YS5pbnZvaWNlQ3VycmVuY3lDb2RlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVJbnZvaWNlQW1vdW50KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOWPkeelqOaxh+eOh+WPmOWMlg0KICAgICJmb3JtRGF0YS5pbnZvaWNlRXhjaGFuZ2VSYXRlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVJbnZvaWNlQW1vdW50KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOi0ueeUqOWIl+ihqOWPmOWMlg0KICAgICJmb3JtRGF0YS5yc0NoYXJnZUxpc3QiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUludm9pY2VBbW91bnQoKQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9LA0KICAgIC8vIOebkeWQrOWPkeelqOexu+Wei+WPmOWMlg0KICAgICJmb3JtRGF0YS5pbnZvaWNlVHlwZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIHRoaXMuY2FsY3VsYXRlSW52b2ljZUFtb3VudCgpDQogICAgICB9DQogICAgfQ0KICB9LA0KICBiZWZvcmVNb3VudCgpIHsNCiAgICB0aGlzLmxvYWRTdGFmZigpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVJbnZvaWNlQXVkaXRDYW5jZWwoKSB7DQogICAgICB0aGlzLmZvcm1EYXRhLmF1ZGl0U3R1ZmZJZCA9IG51bGwNCiAgICAgIHRoaXMuZm9ybURhdGEuYXVkaXRUaW1lID0gbnVsbA0KICAgICAgdGhpcy5mb3JtRGF0YS5hdWRpdFN0YXR1cyA9IG51bGwNCiAgICAgIC8vIOa4heepuuWPkeelqOa1geawtOWPtw0KICAgICAgdGhpcy5mb3JtRGF0YS5pbnZvaWNlQ29kZU5vID0gbnVsbA0KICAgICAgdGhpcy5mb3JtRGF0YS5yc0NoYXJnZUxpc3QuZm9yRWFjaChjaGFyZ2UgPT4gew0KICAgICAgICBjaGFyZ2UuaW52b2ljZUNvZGVObyA9IG51bGwNCiAgICAgIH0pDQogICAgICB0aGlzLiRlbWl0KCJpbnZvaWNlQXVkaXRDYW5jZWwiLCB0aGlzLmZvcm1EYXRhKQ0KICAgIH0sDQogICAgaGFuZGxlSW52b2ljZUFwcGx5Q2FuY2VsKCkgew0KICAgICAgLy8g5Y+R56Wo54q25oCB5pS55Li65pyq5byA56WoDQogICAgICB0aGlzLmZvcm1EYXRhLmFwcGx5U3R1ZmZJZCA9IG51bGwNCiAgICAgIHRoaXMuZm9ybURhdGEuYXBwbGllZFRpbWUgPSBudWxsDQogICAgICB0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPSAidW5pc3N1ZWQiDQoNCiAgICAgIHRoaXMuJGVtaXQoImludm9pY2VBcHBseUNhbmNlbCIsIHRoaXMuZm9ybURhdGEpDQogICAgfSwNCiAgICAvLyDlpITnkIbooajmoLzpgInmi6nlj5jljJYNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IHNlbGVjdGlvbg0KICAgIH0sDQoNCiAgICAvLyDmmL7npLrmibnph4/orr7nva7lvIDnpajpobnnm67lr7nor53moYYNCiAgICBzaG93QmF0Y2hJbnZvaWNpbmdJdGVtRGlhbG9nKCkgew0KICAgICAgdGhpcy5iYXRjaEZvcm0uaW52b2ljaW5nSXRlbSA9IG51bGwNCiAgICAgIHRoaXMuYmF0Y2hJbnZvaWNpbmdJdGVtRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgLy8g56Gu6K6k5om56YeP6K6+572u5byA56Wo6aG555uuDQogICAgY29uZmlybUJhdGNoU2V0SW52b2ljaW5nSXRlbSgpIHsNCiAgICAgIGlmICghdGhpcy5iYXRjaEZvcm0uaW52b2ljaW5nSXRlbSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeW8gOelqOmhueebruWQjeensCIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDkuLrpgInkuK3nmoTooYzorr7nva7lvIDnpajpobnnm64NCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzLmZvckVhY2gocm93ID0+IHsNCiAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSB0aGlzLmJhdGNoRm9ybS5pbnZvaWNpbmdJdGVtDQogICAgICB9KQ0KDQogICAgICB0aGlzLmJhdGNoSW52b2ljaW5nSXRlbURpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LkuLogJHt0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGh9IOmhueiuvue9ruW8gOelqOmhueebrmApDQoNCiAgICAgIC8vIOmHjeaWsOiuoeeul+WPkeelqOmHkeminQ0KICAgICAgdGhpcy5jYWxjdWxhdGVJbnZvaWNlQW1vdW50KCkNCiAgICB9LA0KDQogICAgLy8g5om56YeP6K6+572u6buY6K6k5byA56Wo6aG555uuDQogICAgYmF0Y2hTZXREZWZhdWx0SW52b2ljaW5nSXRlbSgpIHsNCiAgICAgIC8vIOagueaNrui0ueeUqOWQjeensOaZuuiDveiuvue9rum7mOiupOW8gOelqOmhueebrg0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICBjb25zdCBjaGFyZ2VOYW1lID0gcm93LmNoYXJnZU5hbWUgfHwgIiINCg0KICAgICAgICAvLyDmoLnmja7otLnnlKjlkI3np7DljLnphY3pu5jorqTlvIDnpajpobnnm64NCiAgICAgICAgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIui/kOi0uSIpIHx8IGNoYXJnZU5hbWUuaW5jbHVkZXMoIui/kOi+kyIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG6L+Q6LS5Ig0KICAgICAgICB9IGVsc2UgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIuaKpeWFsyIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG5oql5YWz5pyN5Yqh6LS5Ig0KICAgICAgICB9IGVsc2UgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIuaLlui9piIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG5ouW6L2m6LS5Ig0KICAgICAgICB9IGVsc2UgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIua4r+adgiIpIHx8IGNoYXJnZU5hbWUuaW5jbHVkZXMoIueggeWktCIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG5riv5p2C6LS5Ig0KICAgICAgICB9IGVsc2UgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIuaWh+S7tiIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG5paH5Lu26LS5Ig0KICAgICAgICB9IGVsc2UgaWYgKGNoYXJnZU5hbWUuaW5jbHVkZXMoIuaTjeS9nCIpKSB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSAi5Luj55CG5pON5L2c6LS5Ig0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOm7mOiupOiuvue9ruS4uuS7o+eQhuacjeWKoei0uQ0KICAgICAgICAgIHJvdy5pbnZvaWNpbmdJdGVtID0gIuS7o+eQhuacjeWKoei0uSINCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LkuLogJHt0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGh9IOmhueiuvue9rum7mOiupOW8gOelqOmhueebrmApDQoNCiAgICAgIC8vIOmHjeaWsOiuoeeul+WPkeelqOmHkeminQ0KICAgICAgdGhpcy5jYWxjdWxhdGVJbnZvaWNlQW1vdW50KCkNCiAgICB9LA0KDQogICAgLy8g5om56YeP5riF56m65byA56Wo6aG555uuDQogICAgYmF0Y2hDbGVhckludm9pY2luZ0l0ZW0oKSB7DQogICAgICB0aGlzLiRjb25maXJtKCLnoa7lrpropoHmuIXnqbrpgInkuK3pobnnmoTlvIDnpajpobnnm67lkJfvvJ8iLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIGN1c3RvbUNsYXNzOiAibW9kYWwtY29uZmlybSINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLnNlbGVjdGVkUm93cy5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgcm93Lmludm9pY2luZ0l0ZW0gPSBudWxsDQogICAgICAgIH0pDQoNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LmuIXnqbogJHt0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGh9IOmhueeahOW8gOelqOmhueebrmApDQoNCiAgICAgICAgLy8g6YeN5paw6K6h566X5Y+R56Wo6YeR6aKdDQogICAgICAgIHRoaXMuY2FsY3VsYXRlSW52b2ljZUFtb3VudCgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIC8vIOeUqOaIt+WPlua2iOaTjeS9nA0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5byA56Wo6aG555uu5pWw5o2u5qCH5YeG5YyW5Ye95pWwDQogICAgaW52b2ljaW5nSXRlbU5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgY29uc3Qgbm9ybWFsaXplZCA9IHsNCiAgICAgICAgaWQ6IG5vZGUuaW52b2ljaW5nSXRlbU5hbWUsIC8vIOS9v+eUqGludm9pY2luZ0l0ZW1OYW1l5L2c5Li6aWQNCiAgICAgICAgbGFiZWw6IG5vZGUuaW52b2ljaW5nSXRlbU5hbWUsDQogICAgICAgIGludm9pY2luZ0l0ZW1OYW1lOiBub2RlLmludm9pY2luZ0l0ZW1OYW1lDQogICAgICB9DQoNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICBub3JtYWxpemVkLmNoaWxkcmVuID0gbm9kZS5jaGlsZHJlbi5tYXAoY2hpbGQgPT4gdGhpcy5pbnZvaWNpbmdJdGVtTm9ybWFsaXplcihjaGlsZCkpDQogICAgICB9DQoNCiAgICAgIHJldHVybiBub3JtYWxpemVkDQogICAgfSwNCg0KICAgIC8vIOiuoeeul+WPkeelqOmHkeminQ0KICAgIGNhbGN1bGF0ZUludm9pY2VBbW91bnQoKSB7DQogICAgICAvLyDmo4Dmn6Xlv4XopoHnmoTlrZfmrrXmmK/lkKblrZjlnKgNCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS5yc0NoYXJnZUxpc3QgfHwgIUFycmF5LmlzQXJyYXkodGhpcy5mb3JtRGF0YS5yc0NoYXJnZUxpc3QpKSB7DQogICAgICAgIHRoaXMuZm9ybURhdGEuaW52b2ljZU5ldEFtb3VudCA9IDANCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOiuoeeul+i0ueeUqOaYjue7hueahOaAu+mHkemineWSjOeojuminQ0KICAgICAgLy8g56iO6YeRDQogICAgICBsZXQgdmF0QW1vdW50ID0gdGhpcy5mb3JtRGF0YS5yc0NoYXJnZUxpc3QucmVkdWNlKChhY2MsIGNoYXJnZSkgPT4gew0KICAgICAgICAvLyDnoa7kv51zdWJ0b3RhbOWtl+auteWtmOWcqOS4lOS4uuaVsOWtlw0KICAgICAgICBjb25zdCBkdXR5UmF0ZSA9IHBhcnNlRmxvYXQoY2hhcmdlLmR1dHlSYXRlIC8gMTAwKSB8fCAwDQogICAgICAgIGNvbnN0IHZhdEFtb3VudCA9IGR1dHlSYXRlICogKHBhcnNlRmxvYXQoY2hhcmdlLmRuQW1vdW50ICogY2hhcmdlLmRuVW5pdFJhdGUpIHx8IDApDQogICAgICAgIHJldHVybiBhY2MgKyB2YXRBbW91bnQNCiAgICAgIH0sIDApDQogICAgICAvLyDlkKvnqI7ph5ENCiAgICAgIGxldCBuZXRBbW91bnQgPSB0aGlzLmZvcm1EYXRhLnJzQ2hhcmdlTGlzdC5yZWR1Y2UoKGFjYywgY2hhcmdlKSA9PiB7DQogICAgICAgIC8vIOehruS/nXN1YnRvdGFs5a2X5q615a2Y5Zyo5LiU5Li65pWw5a2XDQogICAgICAgIGNvbnN0IHN1YnRvdGFsID0gcGFyc2VGbG9hdChjaGFyZ2Uuc3VidG90YWwpIHx8IDANCiAgICAgICAgcmV0dXJuIGFjYyArIHN1YnRvdGFsDQogICAgICB9LCAwKQ0KICAgICAgLy8g5LiN5ZCr56iO6YeRDQogICAgICBsZXQgZG5BbW91bnQgPSB0aGlzLmZvcm1EYXRhLnJzQ2hhcmdlTGlzdC5yZWR1Y2UoKGFjYywgY2hhcmdlKSA9PiB7DQogICAgICAgIC8vIOehruS/nXN1YnRvdGFs5a2X5q615a2Y5Zyo5LiU5Li65pWw5a2XDQogICAgICAgIGNvbnN0IHN1YnRvdGFsID0gcGFyc2VGbG9hdChjaGFyZ2UuZG5BbW91bnQgKiBjaGFyZ2UuZG5Vbml0UmF0ZSkgfHwgMA0KICAgICAgICByZXR1cm4gYWNjICsgc3VidG90YWwNCiAgICAgIH0sIDApDQoNCiAgICAgIC8vIOS/neWtmOW6lOaUti/lupTku5gNCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLnNhbGVCdXkgPT09ICJzYWxlIikgew0KICAgICAgICB0aGlzLmZvcm1EYXRhLmRuU3VtID0gbmV0QW1vdW50DQogICAgICAgIHRoaXMuZm9ybURhdGEudmF0QW1vdW50ID0gdmF0QW1vdW50DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm1EYXRhLmNuU3VtID0gbmV0QW1vdW50DQogICAgICAgIHRoaXMuZm9ybURhdGEudmF0QW1vdW50ID0gdmF0QW1vdW50DQogICAgICB9DQoNCiAgICAgIC8vIOagueaNruWPkeelqOW4geenjeWSjOaxh+eOh+i/m+ihjOebuOW6lOeahOiuoeeulw0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEuaW52b2ljZUN1cnJlbmN5Q29kZSAmJiB0aGlzLmZvcm1EYXRhLmludm9pY2VFeGNoYW5nZVJhdGUpIHsNCiAgICAgICAgY29uc3QgZXhjaGFuZ2VSYXRlID0gcGFyc2VGbG9hdCh0aGlzLmZvcm1EYXRhLmludm9pY2VFeGNoYW5nZVJhdGUpDQogICAgICAgIGlmIChleGNoYW5nZVJhdGUgPiAwKSB7DQogICAgICAgICAgLy8g5aaC5p6c5rGH546H5aSn5LqOMO+8jOWImei/m+ihjOaxh+eOh+i9rOaNog0KICAgICAgICAgIG5ldEFtb3VudCA9IG5ldEFtb3VudCAqIGV4Y2hhbmdlUmF0ZQ0KICAgICAgICAgIGRuQW1vdW50ID0gZG5BbW91bnQgKiBleGNoYW5nZVJhdGUNCiAgICAgICAgICB2YXRBbW91bnQgPSB2YXRBbW91bnQgKiBleGNoYW5nZVJhdGUNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmmK/lop7lgLznqI7kuJPnlKjlj5HnpajvvIzpnIDopoHliqDkuIo2JeeahOeojuminQ0KICAgICAgbGV0IHRheEFtb3VudCA9IDANCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmludm9pY2VUeXBlID09PSAi5aKe5YC856iO5LiT55So5Y+R56WoIikgew0KICAgICAgICB0YXhBbW91bnQgPSBuZXRBbW91bnQgKiAwLjA2DQogICAgICAgIG5ldEFtb3VudCA9IG5ldEFtb3VudCArIHRheEFtb3VudA0KICAgICAgfQ0KDQogICAgICAvLyDkv53nlZnkuKTkvY3lsI/mlbANCiAgICAgIC8vIOS4jeWQq+eojg0KICAgICAgdGhpcy5mb3JtRGF0YS5pbnZvaWNlTmV0QW1vdW50ID0gcGFyc2VGbG9hdChkbkFtb3VudC50b0ZpeGVkKDIpKQ0KICAgICAgLy8g5Y+R56Wo6YeR6aKdDQogICAgICB0aGlzLmZvcm1EYXRhLmludm9pY2VBbW91bnQgPSAiUk1CIiArIHRoaXMuZm9ybWF0Q3VycmVuY3koZG5BbW91bnQpICsgIiArICIgKyB0aGlzLmZvcm1hdEN1cnJlbmN5KHZhdEFtb3VudCkgKyAiID0gIiArIHRoaXMuZm9ybWF0Q3VycmVuY3kobmV0QW1vdW50KQ0KICAgICAgLy8g57uT566X6YeR6aKdDQogICAgICB0aGlzLmZvcm1EYXRhLnNldHRsZW1lbnRBbW91bnQgPSBwYXJzZUZsb2F0KG5ldEFtb3VudC50b0ZpeGVkKDIpKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5blj5Hnpajph5Hpop3ljaDkvY3nrKbmlofmnKwNCiAgICBnZXRJbnZvaWNlQW1vdW50UGxhY2Vob2xkZXIoKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEucnNDaGFyZ2VMaXN0IHx8IHRoaXMuZm9ybURhdGEucnNDaGFyZ2VMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gIuivt+WFiOa3u+WKoOi0ueeUqOaYjue7hiINCiAgICAgIH0NCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS5pbnZvaWNlQ3VycmVuY3lDb2RlKSB7DQogICAgICAgIHJldHVybiAi6K+36YCJ5oup5Y+R56Wo5biB56eNIg0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmZvcm1EYXRhLmludm9pY2VFeGNoYW5nZVJhdGUpIHsNCiAgICAgICAgcmV0dXJuICLor7fovpPlhaXlj5HnpajmsYfnjociDQogICAgICB9DQogICAgICByZXR1cm4gIumHkemineWwhuiHquWKqOiuoeeulyINCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW6LSn5biB5pi+56S6DQogICAgZm9ybWF0Q3VycmVuY3koYW1vdW50KSB7DQogICAgICBpZiAoIWFtb3VudCAmJiBhbW91bnQgIT09IDApIHJldHVybiAiMC4wMCINCiAgICAgIGNvbnN0IG51bSA9IHBhcnNlRmxvYXQoYW1vdW50KQ0KICAgICAgaWYgKGlzTmFOKG51bSkpIHJldHVybiAiMC4wMCINCiAgICAgIHJldHVybiBudW0udG9GaXhlZCgyKQ0KICAgIH0sDQoNCiAgICAvLyDmj5DkuqTooajljZUNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g6Ieq5Yqo5pS26ZuG6LS555So5YiX6KGo5Lit55qEcmN05Y+377yM5Lul6YCX5Y+35YiG6ZqU5aGr5YWl55u45YWz6K6i5Y2V5Y+35a2X5q61DQogICAgICAgICAgdGhpcy5jb2xsZWN0UmN0TnVtYmVycygpDQogICAgICAgICAgdGhpcy4kZW1pdCgic3VibWl0IiwgdGhpcy5mb3JtRGF0YSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGhhbmRsZUNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJGVtaXQoImNhbmNlbCIpDQogICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICB9LA0KICAgIC8vIOWFs+mXreWvueivneahhg0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgidXBkYXRlOnZpc2libGUiLCBmYWxzZSkNCiAgICB9LA0KICAgIHNlYXJjaEF2YWlsYWJsZUludm9pY2UoKSB7DQogICAgICBjb25zb2xlLmxvZygi5qOA57Si5Y+v55So5Y+R56WoIikNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5YWs5Y+46ZO26KGM6LSm5oi35YiX6KGoDQogICAgZmV0Y2hDb21wYW55QmFua0FjY291bnRzKCkgew0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6YCJ5oup5a+55pa55YWs5Y+4DQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEuY29vcGVyYXRvcklkKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5a+55pa55YWs5Y+4IikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOiwg+eUqEFQSeiOt+WPluivpeWFrOWPuOeahOmTtuihjOi0puaItw0KICAgICAgbGlzdEFjY291bnQoe2JlbG9uZ1RvQ29tcGFueTogdGhpcy5mb3JtRGF0YS5jb29wZXJhdG9ySWR9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuY29tcGFueUJhbmtMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXQ0KICAgICAgICAgIC8vIOWmguaenOayoeaciei0puaIt++8jOaYvuekuuaPkOekug0KICAgICAgICAgIGlmICh0aGlzLmNvbXBhbnlCYW5rTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi6K+l5YWs5Y+45rKh5pyJ6ZO26KGM6LSm5oi36K6w5b2VIikNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5YWs5Y+46ZO26KGM6LSm5oi35aSx6LSlIiwgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluWFrOWPuOmTtuihjOi0puaIt+Wksei0pSIpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbpk7booYzotKbmiLfpgInmi6nlj5jljJYNCiAgICBoYW5kbGVCYW5rQWNjb3VudENoYW5nZShiYW5rQ29kZSkgew0KICAgICAgLy8g5qC55o2u6YCJ5oup55qEYmFua0NvZGXmib7liLDlr7nlupTnmoTpk7booYzotKbmiLfkv6Hmga8NCiAgICAgIGNvbnN0IHNlbGVjdGVkQWNjb3VudCA9IHRoaXMuYXZhaWxhYmxlQmFua0xpc3QuZmluZChpdGVtID0+IGl0ZW0uYmFua0NvZGUgPT09IGJhbmtDb2RlKQ0KDQogICAgICBpZiAoc2VsZWN0ZWRBY2NvdW50KSB7DQogICAgICAgIC8vIOiHquWKqOWhq+WFhemTtuihjOWFqOensOWSjOmTtuihjOi0puWPtw0KICAgICAgICB0aGlzLmZvcm1EYXRhLmNvb3BlcmF0b3JCYW5rRnVsbG5hbWUgPSBzZWxlY3RlZEFjY291bnQuYmFua0JyYW5jaE5hbWUgfHwgIiINCiAgICAgICAgdGhpcy5mb3JtRGF0YS5jb29wZXJhdG9yQmFua0FjY291bnQgPSBzZWxlY3RlZEFjY291bnQuYmFua0FjY291bnQgfHwgIiINCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiOt+WPluWFrOWPuOS/oeaBrw0KICAgIGZldGNoQ29tcGFueUluZm8oY29tcGFueUlkKSB7DQogICAgICBpZiAoIWNvbXBhbnlJZCkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGdldENvbXBhbnkoY29tcGFueUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGNvbXBhbnlJbmZvID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC8vIOabtOaWsOihqOWNleS4reS4juWvueaWueWFrOWPuOebuOWFs+eahOWtl+autQ0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuY29vcGVyYXRvckNvbXBhbnlUaXRsZSA9IGNvbXBhbnlJbmZvLmNvbXBhbnlMb2NhbE5hbWUgfHwgIiINCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmNvb3BlcmF0b3JWYXRTZXJpYWxObyA9IGNvbXBhbnlJbmZvLnRheE5vIHx8ICIiDQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5jb29wZXJhdG9yU2hvcnROYW1lID0gY29tcGFueUluZm8uY29tcGFueVNob3J0TmFtZSB8fCAiIg0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWFrOWPuOS/oeaBr+Wksei0pSIsIGVycm9yKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6Ieq5Yqo5aGr5YWF5oiR5Y+45Y+R56Wo5oqs5aS05ZKM56iO5Y+3DQogICAgYXV0b0ZpbGxDb21wYW55SW5mbyhjb21wYW55Q29kZSkgew0KICAgICAgLy8g5qC55o2u5omA5bGe5YWs5Y+45Luj56CBKEdaUlMvSEtSUy9TWlJTL0daQ0Yp6Ieq5Yqo5aGr5YWF5oiR5Y+45Y+R56Wo5oqs5aS05ZKM56iO5Y+3DQogICAgICBjb25zdCBjb21wYW55SW5mb01hcCA9IHsNCiAgICAgICAgIkdaUlMiOiB7DQogICAgICAgICAgdGl0bGU6ICLlub/lt57nkZ7ml5flm73pmYXotKfov5Dku6PnkIbmnInpmZDlhazlj7giLA0KICAgICAgICAgIHRheE5vOiAiOTE0NDAxMDFNQTU5VVFYWDdCIg0KICAgICAgICB9LA0KICAgICAgICAiSEtSUyI6IHsNCiAgICAgICAgICB0aXRsZTogIummmea4r+eRnuaXl+WbvemZhei0p+i/kOS7o+eQhuaciemZkOWFrOWPuCIsDQogICAgICAgICAgdGF4Tm86ICJISzEyMzQ1Njc4Ig0KICAgICAgICB9LA0KICAgICAgICAiU1pSUyI6IHsNCiAgICAgICAgICB0aXRsZTogIua3seWcs+W4gueRnuaXl+WbvemZhei0p+i/kOS7o+eQhuaciemZkOWFrOWPuCIsDQogICAgICAgICAgdGF4Tm86ICI5MTQ0MDMwME1BNUc5VUI1N1EiDQogICAgICAgIH0sDQogICAgICAgICJHWkNGIjogew0KICAgICAgICAgIHRpdGxlOiAi5bm/5bee5q2j5rO95Zu96ZmF6LSn6L+Q5Luj55CG5pyJ6ZmQ5YWs5Y+4IiwNCiAgICAgICAgICB0YXhObzogIjkxNDQwMTAxTUE5WFJHTEgwRiINCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDojrflj5blr7nlupTlhazlj7jnmoTkv6Hmga8NCiAgICAgIGNvbnN0IGNvbXBhbnlJbmZvID0gY29tcGFueUluZm9NYXBbY29tcGFueUNvZGVdDQoNCiAgICAgIC8vIOWmguaenOaJvuWIsOWvueW6lOeahOWFrOWPuOS/oeaBr++8jOWImeWhq+WFheihqOWNlQ0KICAgICAgaWYgKGNvbXBhbnlJbmZvKSB7DQogICAgICAgIHRoaXMuZm9ybURhdGEucmljaENvbXBhbnlUaXRsZSA9IGNvbXBhbnlJbmZvLnRpdGxlDQogICAgICAgIHRoaXMuZm9ybURhdGEucmljaFZhdFNlcmlhbE5vID0gY29tcGFueUluZm8udGF4Tm8NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yik5pat6KGo5Y2V6aG55piv5ZCm56aB55SoDQogICAgaXNEaXNhYmxlZCgpIHsNCiAgICAgIC8vIOagueaNruS7peS4i+adoeS7tuWIpOaWreihqOWNleaYr+WQpuW6lOivpeemgeeUqO+8mg0KICAgICAgLy8gMS4g5aaC5p6c5Y+R56Wo54q25oCB5Li65bey5byA56WoDQogICAgICBpZiAodGhpcy50eXBlID09PSAiZGViaXROb3RlIiAmJiB0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPT09ICJhcHBsaWVkIikgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICAvLyAyLiDlpoLmnpzmiqXnqI7lt7LplIHlrpoNCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLnRheExvY2tlZCA9PT0gIjEiKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQoNCiAgICAgIC8vIDMuIOWmguaenOW3suaUr+S7mA0KICAgICAgLy8gaWYgKHRoaXMuZm9ybURhdGEuYWN0dWFsUGF5RGF0ZSkgew0KICAgICAgLy8gICByZXR1cm4gdHJ1ZQ0KICAgICAgLy8gfQ0KDQogICAgICAvLyDlpoLmnpzlj5Hnpajlt7LlrqHmoLjvvIzliJnkuI3lj6/nvJbovpENCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmF1ZGl0U3RhdHVzID09PSAi5a6h5qC46YCa6L+HIikgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmsqHmnInnpoHnlKjmnaHku7bvvIzliJnooajljZXlj6/nvJbovpENCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlUmljaEJhbmtDb2RlQ2hhbmdlKHJvdykgew0KICAgICAgdGhpcy5mb3JtRGF0YS5yaWNoQmFua0Z1bGxuYW1lID0gcm93LmJhbmtCcmFuY2hOYW1lDQogICAgICB0aGlzLmZvcm1EYXRhLnJpY2hCYW5rQWNjb3VudCA9IHJvdy5iYW5rQWNjb3VudA0KICAgIH0sDQogICAgLy8g6I635Y+W5Y+R56Wo54q25oCB57G75Z6LDQogICAgZ2V0SW52b2ljZVN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bmlzc3VlZCI6ICJpbmZvIiwNCiAgICAgICAgImlzc3VlZCI6ICJzdWNjZXNzIiwNCiAgICAgICAgImFwcGxpZWQiOiAid2FybmluZyIsDQogICAgICAgICJjYW5jZWxlZCI6ICJkYW5nZXIiDQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgImluZm8iDQogICAgfSwNCiAgICAvLyDojrflj5blj5HnpajnirbmgIHmlofmnKwNCiAgICBnZXRJbnZvaWNlU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgInVuaXNzdWVkIjogIuacquW8gOelqCIsDQogICAgICAgICJpc3N1ZWQiOiAi5bey5byA56WoIiwNCiAgICAgICAgImFwcGxpZWQiOiAi5bey55Sz6K+3IiwNCiAgICAgICAgImNvbmZpcm1lZCI6ICLlt7LlrqHmoLgiLA0KICAgICAgICAiY2FuY2VsZWQiOiAi5bey5L2c5bqfIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICLmnKrnn6UiDQogICAgfSwNCiAgICAvLyDnlLPor7flvIDnpagNCiAgICBhc3luYyBoYW5kbGVBcHBseUludm9pY2UoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDojrflj5blvZPliY3nlKjmiLfkv6Hmga8NCiAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyDQogICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpDQoNCiAgICAgICAgLy8g5pu05paw5Y+R56Wo54q25oCB5Li65bey55Sz6K+3DQogICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgLi4udGhpcy5mb3JtRGF0YSwNCiAgICAgICAgICBpbnZvaWNlU3RhdHVzOiAiYXBwbGllZCIsDQogICAgICAgICAgYXBwbHlTdHVmZklkOiBjdXJyZW50VXNlci5zaWQsIC8vIOW8gOelqOS6uklEDQogICAgICAgICAgYXBwbGllZFRpbWU6IGN1cnJlbnRUaW1lIC8vIOW8gOelqOaXtumXtA0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LCD55SoQVBJ5pu05paw5Y+R56Wo54q25oCBDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlVmF0aW52b2ljZSh1cGRhdGVEYXRhKQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDmlbDmja4NCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPSAiYXBwbGllZCINCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmFwcGx5U3R1ZmZJZCA9IGN1cnJlbnRVc2VyLnNpZA0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuYXBwbGllZFRpbWUgPSBjdXJyZW50VGltZQ0KDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLnlLPor7flvIDnpajmiJDlip8iKQ0KDQogICAgICAgICAgLy8g6YCa55+l54i257uE5Lu25pWw5o2u5Y+Y5YyWDQogICAgICAgICAgdGhpcy4kZW1pdCgiYXBwbHlJbnZvaWNlIiwgdGhpcy5mb3JtRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAi55Sz6K+35byA56Wo5aSx6LSlIikNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi55Sz6K+35byA56Wo5aSx6LSlOiIsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLnlLPor7flvIDnpajlpLHotKXvvIzor7fph43or5UiKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5L+h5oGv5a6h5qC4DQogICAgYXN5bmMgaGFuZGxlQXVkaXRJbnZvaWNlKCkgew0KICAgICAgLy8g6I635Y+W5b2T5YmN55So5oi35L+h5oGvDQogICAgICBjb25zdCBjdXJyZW50VXNlciA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXINCiAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpDQoNCiAgICAgIC8vIOabtOaWsOWuoeaguOS/oeaBrw0KICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgLi4udGhpcy5mb3JtRGF0YSwNCiAgICAgICAgaW52b2ljZVN0YXR1czogImNvbmZpcm1lZCIsDQogICAgICAgIGF1ZGl0U3R1ZmZJZDogY3VycmVudFVzZXIuc2lkLCAvLyDlrqHmoLjkurpJRA0KICAgICAgICBhdWRpdFRpbWU6IGN1cnJlbnRUaW1lLCAvLyDlrqHmoLjml7bpl7QNCiAgICAgICAgYXVkaXRTdGF0dXM6ICJwYXNzIiAvLyDlrqHmoLjnirbmgIENCiAgICAgIH0NCg0KICAgICAgLy8g6YCa55+l54i257uE5Lu25pWw5o2u5Y+Y5YyWDQogICAgICB0aGlzLiRlbWl0KCJhdWRpdEludm9pY2UiLCB0aGlzLmZvcm1EYXRhKQ0KICAgIH0sDQogICAgLy8g5Y+R6YCB5byA56WoDQogICAgYXN5bmMgaGFuZGxlU2VuZEludm9pY2UoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDmo4Dmn6XlrqHmoLjnirbmgIHvvIzlj6rmnInlrqHmoLjpgJrov4fnmoTlj5HnpajmiY3og73lvIDnpagNCiAgICAgICAgaWYgKHRoaXMuZm9ybURhdGEuYXVkaXRTdGF0dXMgIT09ICJwYXNzIikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWPquacieWuoeaguOmAmui/h+eahOWPkeelqOaJjeiDvei/m+ihjOW8gOelqOaTjeS9nCIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDojrflj5blvZPliY3nlKjmiLfkv6Hmga8NCiAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyDQogICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpDQoNCiAgICAgICAgLy8g5pu05paw5byA56Wo5L+h5oGvDQogICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgLi4udGhpcy5mb3JtRGF0YSwNCiAgICAgICAgICBpbnZvaWNlU3RhdHVzOiAiaXNzdWVkIiwgLy8g5Y+R56Wo54q25oCB5Li65bey5byA56WoDQogICAgICAgICAgaXNzdWVkU3R1ZmZJZDogY3VycmVudFVzZXIuc2lkLCAvLyDlvIDnpajkurpJRA0KICAgICAgICAgIGlzc3VlZFRpbWU6IGN1cnJlbnRUaW1lIC8vIOW8gOelqOaXtumXtA0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LCD55SoQVBJ5pu05paw5Y+R56Wo5byA56Wo5L+h5oGvDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlVmF0aW52b2ljZSh1cGRhdGVEYXRhKQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDmlbDmja4NCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPSAiaXNzdWVkIg0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuaXNzdWVkU3R1ZmZJZCA9IGN1cnJlbnRVc2VyLnNpZA0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuaXNzdWVkVGltZSA9IGN1cnJlbnRUaW1lDQoNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWPkemAgeW8gOelqOaIkOWKnyIpDQoNCiAgICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bmlbDmja7lj5jljJYNCiAgICAgICAgICB0aGlzLiRlbWl0KCJzZW5kSW52b2ljZSIsIHRoaXMuZm9ybURhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgIuWPkemAgeW8gOelqOWksei0pSIpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWPkemAgeW8gOelqOWksei0pToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Y+R6YCB5byA56Wo5aSx6LSl77yM6K+36YeN6K+VIikNCiAgICAgIH0NCiAgICB9DQogICAgLA0KICAgIC8vIOWIpOaWreaYr+WQpuWPr+S7peeUs+ivt+W8gOelqA0KICAgIGNhbkFwcGx5SW52b2ljZSgpIHsNCiAgICAgIC8vIOajgOafpeaYr+WQpuacieWPkeelqElEDQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEuaW52b2ljZUlkKSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KDQogICAgICAvLyDmo4Dmn6Xlj5HnpajnirbmgIENCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPT09ICJpc3N1ZWQiKSB7DQogICAgICAgIHJldHVybiBmYWxzZSAvLyDlt7LlvIDnpajkuI3og73lho3mrKHnlLPor7cNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEuaW52b2ljZVN0YXR1cyA9PT0gImFwcGxpZWQiKSB7DQogICAgICAgIHJldHVybiBmYWxzZSAvLyDlt7LnlLPor7fkuI3og73ph43lpI3nlLPor7cNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5oql56iO6ZSB5a6aDQogICAgICBpZiAodGhpcy5mb3JtRGF0YS50YXhMb2NrZWQgPT09ICIxIikgew0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5pSv5LuY77yI5aaC5p6c6ZyA6KaB55qE6K+d77yJDQogICAgICAvLyBpZiAodGhpcy5mb3JtRGF0YS5hY3R1YWxQYXlEYXRlKSB7DQogICAgICAvLyAgIHJldHVybiBmYWxzZQ0KICAgICAgLy8gfQ0KDQogICAgICByZXR1cm4gdHJ1ZQ0KICAgIH0NCiAgICAsDQogICAgLy8g6I635Y+W55Sz6K+35L+h5oGvDQogICAgZ2V0QXBwbHlJbmZvKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEuaW52b2ljZVN0YXR1cyA9PT0gImFwcGxpZWQiKSB7DQogICAgICAgIC8vIOWmguaenOW3sueUs+ivt+W8gOelqO+8jOaYvuekuueUs+ivt+S6uivml7bpl7QNCiAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyDQogICAgICAgIGxldCBhcHBseVRpbWUgPSAiIg0KDQogICAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmlzc3VlZFRpbWUpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgYXBwbHlUaW1lID0gbmV3IERhdGUodGhpcy5mb3JtRGF0YS5pc3N1ZWRUaW1lKS50b0xvY2FsZVN0cmluZygiemgtQ04iLCB7DQogICAgICAgICAgICAgIHllYXI6ICJudW1lcmljIiwNCiAgICAgICAgICAgICAgbW9udGg6ICIyLWRpZ2l0IiwNCiAgICAgICAgICAgICAgZGF5OiAiMi1kaWdpdCIsDQogICAgICAgICAgICAgIGhvdXI6ICIyLWRpZ2l0IiwNCiAgICAgICAgICAgICAgbWludXRlOiAiMi1kaWdpdCINCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgYXBwbHlUaW1lID0gIuaXtumXtOagvOW8j+mUmeivryINCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCB1c2VyTmFtZSA9IHRoaXMuZ2V0TmFtZShjdXJyZW50VXNlci5zaWQpIHx8ICLmnKrnn6XnlKjmiLciDQogICAgICAgIHJldHVybiBgJHt1c2VyTmFtZX0ke2FwcGx5VGltZX1gDQogICAgICB9DQogICAgICBpZiAodGhpcy5mb3JtRGF0YS5pbnZvaWNlU3RhdHVzID09PSAiaXNzdWVkIikgew0KICAgICAgICByZXR1cm4gIuW3suW8gOelqCINCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLnRheExvY2tlZCA9PT0gIjEiKSB7DQogICAgICAgIHJldHVybiAi5bey5oql56iO6ZSB5a6aIg0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEuYWN0dWFsUGF5RGF0ZSkgew0KICAgICAgICByZXR1cm4gIuW3suaUr+S7mCINCiAgICAgIH0NCiAgICAgIHJldHVybiAi5pyq55Sz6K+3Ig0KICAgIH0NCiAgICAsDQogICAgZ2V0TmFtZShpZCkgew0KICAgICAgaWYgKGlkKSB7DQogICAgICAgIGxldCBzdGFmZiA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IGlkKVswXQ0KICAgICAgICBpZiAoc3RhZmYpIHsNCiAgICAgICAgICByZXR1cm4gc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBzdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSArIHN0YWZmLnN0YWZmU2hvcnROYW1lDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmV0dXJuICIiDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAiIg0KICAgICAgfQ0KICAgIH0NCiAgICAsDQogICAgbG9hZFN0YWZmKCkgew0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3QuYWxsUnNTdGFmZkxpc3QpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldEFsbFJzU3RhZmZMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5zdGFmZkxpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uZGVwdC5kZXB0TG9jYWxOYW1lID09PSAi5Lia5Yqh6YOoIikNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc3RhZmZMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmRlcHQuZGVwdExvY2FsTmFtZSA9PT0gIuS4muWKoemDqCIpDQogICAgICB9DQogICAgfQ0KICAgICwNCg0KICAgIC8vIOiHquWKqOaUtumbhui0ueeUqOWIl+ihqOS4reeahHJjdOWPt++8jOS7pemAl+WPt+WIhumalOWhq+WFpeebuOWFs+iuouWNleWPt+Wtl+autQ0KICAgIGNvbGxlY3RSY3ROdW1iZXJzKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm1EYXRhLnJzQ2hhcmdlTGlzdCB8fCAhQXJyYXkuaXNBcnJheSh0aGlzLmZvcm1EYXRhLnJzQ2hhcmdlTGlzdCkpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOaUtumbhuaJgOacieS4jemHjeWkjeeahHJjdOWPtw0KICAgICAgY29uc3QgcmN0TnVtYmVycyA9IG5ldyBTZXQoKQ0KICAgICAgdGhpcy5mb3JtRGF0YS5yc0NoYXJnZUxpc3QuZm9yRWFjaChjaGFyZ2UgPT4gew0KICAgICAgICBpZiAoY2hhcmdlLnNxZFJjdE5vICYmIGNoYXJnZS5zcWRSY3ROby50cmltKCkpIHsNCiAgICAgICAgICByY3ROdW1iZXJzLmFkZChjaGFyZ2Uuc3FkUmN0Tm8udHJpbSgpKQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICAvLyDlsIZyY3Tlj7fovazmjaLkuLrmlbDnu4TlubbmjpLluo/vvIznhLblkI7ku6XpgJflj7fliIbpmpQNCiAgICAgIGNvbnN0IHJjdEFycmF5ID0gQXJyYXkuZnJvbShyY3ROdW1iZXJzKS5zb3J0KCkNCiAgICAgIHRoaXMuZm9ybURhdGEucmVsYXRlZE9yZGVyTm8gPSByY3RBcnJheS5qb2luKCIsIikNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["VatinvoiceDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4j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file": "VatinvoiceDialog.vue", "sourceRoot": "src/views/system/vatinvoice/components", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    v-dialogDragWidth\r\n    :close-on-click-modal=\"false\"\r\n    :modal-append-to-body=\"false\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    append-to-body\r\n    modal\r\n    width=\"80%\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" class=\"edit\" label-width=\"80px\" size=\"mini\">\r\n      <!-- 第一行 - 基本信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票流水号\">\r\n            <el-input v-model=\"formData.invoiceCodeNo\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票流水号\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"进销标志\">\r\n            <el-select v-model=\"formData.saleBuy\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"进销标志\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"销项\" value=\"sale\"/>\r\n              <el-option label=\"进项\" value=\"buy\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票性质\">\r\n            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->\r\n            <el-select v-model=\"formData.taxClass\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票性质\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"主营业务收入\" value=\"主营业务收入\"/>\r\n              <el-option label=\"非主营业务收入\" value=\"非主营业务收入\"/>\r\n              <el-option label=\"营业外收入\" value=\"营业外收入\"/>\r\n              <el-option label=\"成本\" value=\"成本\"/>\r\n              <el-option label=\"费用\" value=\"费用\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票类型\">\r\n            <el-select v-model=\"formData.invoiceType\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票类型\" style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"增值税专用发票\" value=\"增值税专用发票\"/>\r\n              <el-option label=\"普通发票\" value=\"普通发票\"/>\r\n              <el-option label=\"收据\" value=\"收据\"/>\r\n              <el-option label=\"无票收支\" value=\"无票收支\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"接收合开\">\r\n            <el-row>\r\n              <el-col :offset=\"1\" :span=\"8\">\r\n                <el-checkbox v-model=\"formData.mergeInvoice\" :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" false-label=\"0\"\r\n                             true-label=\"1\"\r\n                >\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-tag :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                        :type=\"getInvoiceStatusType(formData.invoiceStatus)\"\r\n                >{{ getInvoiceStatusText(formData.invoiceStatus) }}\r\n                </el-tag>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票号码\">\r\n            <el-row>\r\n              <el-col :span=\"18\">\r\n                <el-input v-model=\"formData.invoiceOfficalNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票号码\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-popover\r\n                  placement=\"top-start\"\r\n                  title=\"发票附件管理\"\r\n                  trigger=\"hover\"\r\n                  width=\"300\"\r\n                >\r\n                  <div>\r\n                    <file-upload\r\n                      :class=\"isDisabled()?'disable-form':''\"\r\n                      :file-type=\"['pdf']\"\r\n                      :is-tip-flex=\"true\"\r\n                      :value=\"formData.invoiceAttachment\"\r\n                      @input=\"formData.invoiceAttachment=$event\"\r\n                    />\r\n                  </div>\r\n                  <template #reference>\r\n                    <el-button size=\"mini\" type=\"text\">\r\n                      [发票]\r\n                    </el-button>\r\n                  </template>\r\n                </el-popover>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行 - 公司和账户信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属公司\">\r\n                <el-input v-model=\"formData.invoiceBelongsTo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"所属公司\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"我司账户\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"formData.richBankCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\" @return=\"formData.richBankCode=$event\"\r\n                             :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                             @returnData=\"handleRichBankCodeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方公司\">\r\n                <company-select :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"formData.cooperatorId\" :placeholder=\"''\"\r\n                                @return=\"formData.cooperatorId=$event\"\r\n                                :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方账户\">\r\n                <el-select v-model=\"formData.cooperatorBankCode\" :class=\"{'disable-form': isDisabled()}\"\r\n                           :disabled=\"isDisabled()\"\r\n                           placeholder=\"对方账户\" style=\"width: 100%\"\r\n                           @change=\"handleBankAccountChange\" @click.native=\"fetchCompanyBankAccounts\"\r\n                >\r\n                  <el-option v-for=\"item in availableBankList\" :key=\"item.bankAccId\"\r\n                             :label=\"item.bankAccCode+ '('+item.bankAccount+')'\" :value=\"item.bankAccCode\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.richCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.cooperatorCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"8\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.opRemark\" :minrows=\"3\" :rows=\"2\"\r\n                        placeholder=\"开票要求\" type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.relatedOrderNo\" :minrows=\"3\" :rows=\"2\" placeholder=\"相关订单号\"\r\n                        type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第四行 - 税号信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.richVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.cooperatorVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\" placeholder=\"对方纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.richBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.cooperatorBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.richBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.cooperatorBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算币种\">\r\n                <tree-select :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" :pass=\"formData.settlementCurrencyCode\"\r\n                             :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                             style=\"width: 100%\" @return=\"formData.settlementCurrencyCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算金额\">\r\n                <el-input v-model=\"formData.settlementAmount\" class=\"disable-form\"\r\n                          disabled placeholder=\"结算金额\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"发票汇率\">\r\n                <el-input v-model=\"formData.invoiceExchangeRate\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票汇率\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"发票金额\">\r\n                <el-input v-model=\"formData.invoiceAmount\" :placeholder=\"getInvoiceAmountPlaceholder()\"\r\n                          class=\"disable-form\"\r\n                          disabled\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-input v-model=\"formData.invoiceRemark\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                    :minrows=\"6\" :rows=\"6\"\r\n                    placeholder=\"税务发票备注栏\" type=\"textarea\"\r\n          />\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-col>\r\n            <el-form-item label=\"期望支付日\">\r\n              <el-date-picker v-model=\"formData.expectedPayDate\"\r\n                              clearable\r\n                              placeholder=\"期望支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"批复支付日\">\r\n              <el-date-picker v-model=\"formData.approvedPayDate\"\r\n                              clearable\r\n                              placeholder=\"批复支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"实际支付日\">\r\n              <el-date-picker v-model=\"formData.actualPayDate\"\r\n                              clearable\r\n                              placeholder=\"实际支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"报税月份\">\r\n              <el-input v-model=\"formData.belongsToMonth\"\r\n                        :class=\"{'disable-form': isDisabled() || type === 'debitNote'}\"\r\n                        :disabled=\"isDisabled() || type === 'debitNote'\"\r\n                        class=\"yellow-bg\" placeholder=\"2025/7/31\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-col>\r\n      </el-row>\r\n\r\n\r\n      <el-divider></el-divider>\r\n\r\n      <!-- 发票明细表格 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"24\">\r\n          <!-- 批量操作工具栏 -->\r\n          <div style=\"margin-bottom: 10px; display: flex; align-items: center; gap: 10px;\">\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"showBatchInvoicingItemDialog\"\r\n            >\r\n              批量设置开票项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"batchSetDefaultInvoicingItem\"\r\n            >\r\n              批量设置默认项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"batchClearInvoicingItem\"\r\n            >\r\n              批量清空开票项目\r\n            </el-button>\r\n            <span style=\"margin-left: 20px; color: #606266;\">\r\n              已选择 {{ selectedRows.length }} 项\r\n            </span>\r\n          </div>\r\n\r\n          <el-table\r\n            ref=\"chargeTable\"\r\n            :data=\"formData.rsChargeList\"\r\n            border\r\n            size=\"mini\"\r\n            style=\"width: 100%\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"35\"/>\r\n            <el-table-column align=\"center\" label=\"账单编号\" prop=\"debitNoteId\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.debitNoteId }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"RCT号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"所属服务\" prop=\"serviceLocalName\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.sqdServiceTypeId == 0 ? \"客户应收\" : scope.row.serviceLocalName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"费用名称\" prop=\"chargeName\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"备注\" prop=\"chargeRemark\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"收付标志\" prop=\"isReceivingOrPaying\" width=\"80\">\r\n              <template #default=\"scope\">{{ scope.row.isReceivingOrPaying == 0 ? \"应收\" : \"应付\" }}</template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"报价币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单价\" prop=\"dnUnitRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"数量\" prop=\"dnAmount\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单位\" prop=\"dnUnitCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算汇率\" prop=\"basicCurrencyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税率\" prop=\"dutyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税金\" prop=\"dutyRate\" width=\"80\">\r\n              <template #default=\"scope\">\r\n                {{ (scope.row.dutyRate / 100) * scope.row.dnAmount * scope.row.dnUnitRate }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"含税小计\" prop=\"subtotal\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"开票项目名称\" prop=\"invoicingItem\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                <treeselect v-model=\"scope.row.invoicingItem\"\r\n                            :class=\"{'disable-form': isDisabled()}\"\r\n                            :default-expand-level=\"1\"\r\n                            :disable-branch-nodes=\"true\"\r\n                            :disabled=\"isDisabled()\"\r\n                            :normalizer=\"invoicingItemNormalizer\"\r\n                            :options=\"invoicingItemOptions\"\r\n                            :show-count=\"true\"\r\n                            :z-index=\"9999\"\r\n                            append-to-body\r\n                            placeholder=\"开票项目名称\"\r\n                            searchable\r\n                />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"税收编码\" prop=\"taxCode\" width=\"100\"/>\r\n          </el-table>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 批量设置开票项目对话框 -->\r\n      <el-dialog\r\n        :visible.sync=\"batchInvoicingItemDialogVisible\"\r\n        append-to-body\r\n        title=\"批量设置开票项目\"\r\n        width=\"500px\"\r\n      >\r\n        <el-form :model=\"batchForm\" label-width=\"120px\">\r\n          <el-form-item label=\"开票项目名称\">\r\n            <treeselect\r\n              v-model=\"batchForm.invoicingItem\"\r\n              :default-expand-level=\"1\"\r\n              :disable-branch-nodes=\"true\"\r\n              :normalizer=\"invoicingItemNormalizer\"\r\n              :options=\"invoicingItemOptions\"\r\n              :show-count=\"true\"\r\n              placeholder=\"请选择开票项目名称\"\r\n              searchable\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"batchInvoicingItemDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"confirmBatchSetInvoicingItem\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 操作按钮组 -->\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n        <el-col :span=\"3\">\r\n          <el-button icon=\"el-icon-check\" size=\"mini\" type=\"primary\">√默认对冲</el-button>\r\n          <div>已选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">智选</el-button>\r\n          <div>未选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">反选</el-button>\r\n          <div>全部总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"isDisabled() || formData.auditStuffId !== null\"\r\n            size=\"mini\"\r\n            type=\"primary\" @click=\"submitForm\"\r\n          >\r\n            保存草稿\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button v-if=\"formData.applyStuffId === null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleApplyInvoice\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"申请开票\" : \"确认账单\" }}\r\n            </el-button>\r\n            <el-button v-show=\"formData.applyStuffId !== null\" :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleInvoiceApplyCancel\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"取消开票\" : \"取消账单\" }}\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.applyStuffId) }}</div>\r\n          <div>{{ formData.appliedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button\r\n              v-if=\"formData.auditStuffId === null\"\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAuditInvoice\"\r\n            >\r\n              财务审核\r\n            </el-button>\r\n            <el-button\r\n              v-else\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId === null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleInvoiceAuditCancel\"\r\n            >\r\n              取消审核\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.auditStuffId) }}</div>\r\n          <div>{{ formData.auditTime }}</div>\r\n        </el-col>\r\n        <!--<el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"type === 'debitNote' || formData.auditStatus !== '审核通过'\"\r\n            size=\"mini\"\r\n            type=\"success\"\r\n            @click=\"handleSendInvoice\"\r\n          >\r\n            发送开票\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>-->\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"warning\">打印</el-button>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"info\">报税锁定</el-button>\r\n          <div>报税人+时间</div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {getCompany} from \"@/api/system/company\"\r\nimport {listAccount} from \"@/api/system/account\"\r\nimport {updateVatinvoice} from \"@/api/system/vatInvoice\"\r\nimport FileUpload from \"@/components/FileUpload\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport store from \"@/store\"\r\nimport {updateDebitNoteByInvoiceId} from \"@/api/system/debitnote\"\r\n\r\n// 防抖函数\r\nfunction debounce(fn, delay) {\r\n  let timer = null\r\n  return function () {\r\n    const context = this\r\n    const args = arguments\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      fn.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"VatinvoiceDialog\",\r\n  components: {\r\n    FileUpload,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 是否显示对话框\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 标题\r\n    title: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 表单验证规则\r\n    rules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 发票明细列表\r\n    invoiceItems: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 银行账户列表\r\n    bankAccountList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 内部对话框可见性状态\r\n      dialogVisible: false,\r\n      // 表单数据的副本\r\n      formData: {},\r\n      // 发票明细列表的副本\r\n      invoiceItemList: [],\r\n      // 防抖后的获取公司信息方法\r\n      debouncedFetchCompanyInfo: null,\r\n      // 公司银行账户列表\r\n      companyBankList: [],\r\n      // 开票项目选项数据\r\n      invoicingItemOptions: [\r\n        {\r\n          id: \"1\",\r\n          invoicingItemName: \"经纪代理服务\",\r\n          children: [\r\n            {id: \"1-1\", invoicingItemName: \"代理运费\"},\r\n            {id: \"1-2\", invoicingItemName: \"国际货物运输代理服务费\"},\r\n            {id: \"1-3\", invoicingItemName: \"代理港杂费\"},\r\n            {id: \"1-4\", invoicingItemName: \"国际货物运输代理服务\"},\r\n            {id: \"1-5\", invoicingItemName: \"代理报关服务费\"},\r\n            {id: \"1-6\", invoicingItemName: \"代理服务费\"},\r\n            {id: \"1-7\", invoicingItemName: \"代理报关费\"},\r\n            {id: \"1-8\", invoicingItemName: \"代理拖车费\"},\r\n            {id: \"1-9\", invoicingItemName: \"国际货物运输代理服务-代理运费\"},\r\n            {id: \"1-10\", invoicingItemName: \"代理国内运费\"},\r\n            {id: \"1-11\", invoicingItemName: \"国际货物运输代理海运费\"},\r\n            {id: \"1-13\", invoicingItemName: \"运输代理费\"},\r\n            {id: \"1-14\", invoicingItemName: \"货物运输代理服务费\"},\r\n            {id: \"1-15\", invoicingItemName: \"国际货物运输代理港杂费\"},\r\n            {id: \"1-16\", invoicingItemName: \"国际货物运输代理运费\"},\r\n            {id: \"1-17\", invoicingItemName: \"货物运输代理费\"},\r\n            {id: \"1-18\", invoicingItemName: \"国际货物运输代理费\"},\r\n            {id: \"1-19\", invoicingItemName: \"代理杂费\"},\r\n            {id: \"1-20\", invoicingItemName: \"代理文件费\"},\r\n            {id: \"1-21\", invoicingItemName: \"代理设备交接单费用\"},\r\n            {id: \"1-22\", invoicingItemName: \"代理舱单申报费\"},\r\n            {id: \"1-23\", invoicingItemName: \"代理操作费\"},\r\n            {id: \"1-24\", invoicingItemName: \"代理封条费\"},\r\n            {id: \"1-25\", invoicingItemName: \"代理码头操作费\"},\r\n            {id: \"1-26\", invoicingItemName: \"代理电放费\"},\r\n            {id: \"1-27\", invoicingItemName: \"代理核重费\"}\r\n          ]\r\n        }\r\n      ],\r\n      // 批量选择相关数据\r\n      selectedRows: [], // 选中的行数据\r\n      batchInvoicingItemDialogVisible: false, // 批量设置开票项目对话框可见性\r\n      batchForm: {\r\n        invoicingItem: null // 批量设置的开票项目\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList\r\n    availableBankList() {\r\n      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟\r\n    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val\r\n        if (val) {\r\n          // 当对话框显示时，复制传入的数据\r\n          this.formData = JSON.parse(JSON.stringify(this.form))\r\n          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))\r\n\r\n          // 确保发票附件字段存在\r\n          if (!this.formData.invoiceAttachment) {\r\n            this.formData.invoiceAttachment = \"\"\r\n          }\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 初始化时计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听 form prop 变化，当父组件更新发票数据时自动同步到子组件\r\n    form: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          // 当 form prop 变化时，更新内部的 formData\r\n          this.formData = JSON.parse(JSON.stringify(newVal))\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 重新计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听 invoiceItems prop 变化，当父组件更新费用明细时自动同步到子组件\r\n    // invoiceItems: {\r\n    //   handler(newVal) {\r\n    //     if (newVal && Array.isArray(newVal)) {\r\n    //       // 当 invoiceItems prop 变化时，更新内部的 invoiceItemList\r\n    //       this.invoiceItemList = JSON.parse(JSON.stringify(newVal))\r\n\r\n    //       // 重新计算发票金额\r\n    //       this.$nextTick(() => {\r\n    //         this.calculateInvoiceAmount()\r\n    //       })\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 监听对方公司ID变化\r\n    \"formData.cooperatorId\": {\r\n      handler(newVal, oldVal) {\r\n        // 只有当值真正变化时才触发查询\r\n        this.debouncedFetchCompanyInfo(newVal)\r\n      }\r\n    },\r\n    // 监听所属公司变化，自动填充我司发票抬头和税号\r\n    \"formData.invoiceBelongsTo\": {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.autoFillCompanyInfo(newVal)\r\n        }\r\n      }\r\n    },\r\n    // 发票金额自动根据发票汇率/发票币种/发票明细表的含税小计计算\r\n    \"formData.invoiceCurrencyCode\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听发票汇率变化\r\n    \"formData.invoiceExchangeRate\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听费用列表变化\r\n    \"formData.rsChargeList\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听发票类型变化\r\n    \"formData.invoiceType\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    handleInvoiceAuditCancel() {\r\n      this.formData.auditStuffId = null\r\n      this.formData.auditTime = null\r\n      this.formData.auditStatus = null\r\n      // 清空发票流水号\r\n      this.formData.invoiceCodeNo = null\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        charge.invoiceCodeNo = null\r\n      })\r\n      this.$emit(\"invoiceAuditCancel\", this.formData)\r\n    },\r\n    handleInvoiceApplyCancel() {\r\n      // 发票状态改为未开票\r\n      this.formData.applyStuffId = null\r\n      this.formData.appliedTime = null\r\n      this.formData.invoiceStatus = \"unissued\"\r\n\r\n      this.$emit(\"invoiceApplyCancel\", this.formData)\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    // 显示批量设置开票项目对话框\r\n    showBatchInvoicingItemDialog() {\r\n      this.batchForm.invoicingItem = null\r\n      this.batchInvoicingItemDialogVisible = true\r\n    },\r\n\r\n    // 确认批量设置开票项目\r\n    confirmBatchSetInvoicingItem() {\r\n      if (!this.batchForm.invoicingItem) {\r\n        this.$message.warning(\"请选择开票项目名称\")\r\n        return\r\n      }\r\n\r\n      // 为选中的行设置开票项目\r\n      this.selectedRows.forEach(row => {\r\n        row.invoicingItem = this.batchForm.invoicingItem\r\n      })\r\n\r\n      this.batchInvoicingItemDialogVisible = false\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量设置默认开票项目\r\n    batchSetDefaultInvoicingItem() {\r\n      // 根据费用名称智能设置默认开票项目\r\n      this.selectedRows.forEach(row => {\r\n        const chargeName = row.chargeName || \"\"\r\n\r\n        // 根据费用名称匹配默认开票项目\r\n        if (chargeName.includes(\"运费\") || chargeName.includes(\"运输\")) {\r\n          row.invoicingItem = \"代理运费\"\r\n        } else if (chargeName.includes(\"报关\")) {\r\n          row.invoicingItem = \"代理报关服务费\"\r\n        } else if (chargeName.includes(\"拖车\")) {\r\n          row.invoicingItem = \"代理拖车费\"\r\n        } else if (chargeName.includes(\"港杂\") || chargeName.includes(\"码头\")) {\r\n          row.invoicingItem = \"代理港杂费\"\r\n        } else if (chargeName.includes(\"文件\")) {\r\n          row.invoicingItem = \"代理文件费\"\r\n        } else if (chargeName.includes(\"操作\")) {\r\n          row.invoicingItem = \"代理操作费\"\r\n        } else {\r\n          // 默认设置为代理服务费\r\n          row.invoicingItem = \"代理服务费\"\r\n        }\r\n      })\r\n\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置默认开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量清空开票项目\r\n    batchClearInvoicingItem() {\r\n      this.$confirm(\"确定要清空选中项的开票项目吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n        customClass: \"modal-confirm\"\r\n      }).then(() => {\r\n        this.selectedRows.forEach(row => {\r\n          row.invoicingItem = null\r\n        })\r\n\r\n        this.$message.success(`已清空 ${this.selectedRows.length} 项的开票项目`)\r\n\r\n        // 重新计算发票金额\r\n        this.calculateInvoiceAmount()\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n\r\n    // 开票项目数据标准化函数\r\n    invoicingItemNormalizer(node) {\r\n      const normalized = {\r\n        id: node.invoicingItemName, // 使用invoicingItemName作为id\r\n        label: node.invoicingItemName,\r\n        invoicingItemName: node.invoicingItemName\r\n      }\r\n\r\n      if (node.children && node.children.length > 0) {\r\n        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))\r\n      }\r\n\r\n      return normalized\r\n    },\r\n\r\n    // 计算发票金额\r\n    calculateInvoiceAmount() {\r\n      // 检查必要的字段是否存在\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        this.formData.invoiceNetAmount = 0\r\n        return\r\n      }\r\n\r\n      // 计算费用明细的总金额和税额\r\n      // 税金\r\n      let vatAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const dutyRate = parseFloat(charge.dutyRate / 100) || 0\r\n        const vatAmount = dutyRate * (parseFloat(charge.dnAmount * charge.dnUnitRate) || 0)\r\n        return acc + vatAmount\r\n      }, 0)\r\n      // 含税金\r\n      let netAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.subtotal) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n      // 不含税金\r\n      let dnAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.dnAmount * charge.dnUnitRate) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n\r\n      // 保存应收/应付\r\n      if (this.formData.saleBuy === \"sale\") {\r\n        this.formData.dnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      } else {\r\n        this.formData.cnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      }\r\n\r\n      // 根据发票币种和汇率进行相应的计算\r\n      if (this.formData.invoiceCurrencyCode && this.formData.invoiceExchangeRate) {\r\n        const exchangeRate = parseFloat(this.formData.invoiceExchangeRate)\r\n        if (exchangeRate > 0) {\r\n          // 如果汇率大于0，则进行汇率转换\r\n          netAmount = netAmount * exchangeRate\r\n          dnAmount = dnAmount * exchangeRate\r\n          vatAmount = vatAmount * exchangeRate\r\n        }\r\n      }\r\n\r\n      // 如果是增值税专用发票，需要加上6%的税额\r\n      let taxAmount = 0\r\n      if (this.formData.invoiceType === \"增值税专用发票\") {\r\n        taxAmount = netAmount * 0.06\r\n        netAmount = netAmount + taxAmount\r\n      }\r\n\r\n      // 保留两位小数\r\n      // 不含税\r\n      this.formData.invoiceNetAmount = parseFloat(dnAmount.toFixed(2))\r\n      // 发票金额\r\n      this.formData.invoiceAmount = \"RMB\" + this.formatCurrency(dnAmount) + \" + \" + this.formatCurrency(vatAmount) + \" = \" + this.formatCurrency(netAmount)\r\n      // 结算金额\r\n      this.formData.settlementAmount = parseFloat(netAmount.toFixed(2))\r\n    },\r\n\r\n    // 获取发票金额占位符文本\r\n    getInvoiceAmountPlaceholder() {\r\n      if (!this.formData.rsChargeList || this.formData.rsChargeList.length === 0) {\r\n        return \"请先添加费用明细\"\r\n      }\r\n      if (!this.formData.invoiceCurrencyCode) {\r\n        return \"请选择发票币种\"\r\n      }\r\n      if (!this.formData.invoiceExchangeRate) {\r\n        return \"请输入发票汇率\"\r\n      }\r\n      return \"金额将自动计算\"\r\n    },\r\n\r\n    // 格式化货币显示\r\n    formatCurrency(amount) {\r\n      if (!amount && amount !== 0) return \"0.00\"\r\n      const num = parseFloat(amount)\r\n      if (isNaN(num)) return \"0.00\"\r\n      return num.toFixed(2)\r\n    },\r\n\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n          this.collectRctNumbers()\r\n          this.$emit(\"submit\", this.formData)\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    handleCancel() {\r\n      this.$emit(\"cancel\")\r\n      this.handleClose()\r\n    },\r\n    // 关闭对话框\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false)\r\n    },\r\n    searchAvailableInvoice() {\r\n      console.log(\"检索可用发票\")\r\n    },\r\n\r\n    // 获取公司银行账户列表\r\n    fetchCompanyBankAccounts() {\r\n      // 检查是否有选择对方公司\r\n      if (!this.formData.cooperatorId) {\r\n        this.$message.warning(\"请先选择对方公司\")\r\n        return\r\n      }\r\n\r\n      // 调用API获取该公司的银行账户\r\n      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {\r\n        if (response.code === 200) {\r\n          this.companyBankList = response.rows || []\r\n          // 如果没有账户，显示提示\r\n          if (this.companyBankList.length === 0) {\r\n            this.$message.info(\"该公司没有银行账户记录\")\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司银行账户失败\", error)\r\n        this.$message.error(\"获取公司银行账户失败\")\r\n      })\r\n    },\r\n\r\n    // 处理银行账户选择变化\r\n    handleBankAccountChange(bankCode) {\r\n      // 根据选择的bankCode找到对应的银行账户信息\r\n      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)\r\n\r\n      if (selectedAccount) {\r\n        // 自动填充银行全称和银行账号\r\n        this.formData.cooperatorBankFullname = selectedAccount.bankBranchName || \"\"\r\n        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || \"\"\r\n      }\r\n    },\r\n    // 获取公司信息\r\n    fetchCompanyInfo(companyId) {\r\n      if (!companyId) {\r\n        return\r\n      }\r\n      getCompany(companyId).then(response => {\r\n        if (response.code === 200) {\r\n          const companyInfo = response.data\r\n          // 更新表单中与对方公司相关的字段\r\n          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || \"\"\r\n          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || \"\"\r\n          this.formData.cooperatorShortName = companyInfo.companyShortName || \"\"\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司信息失败\", error)\r\n      })\r\n    },\r\n\r\n    // 自动填充我司发票抬头和税号\r\n    autoFillCompanyInfo(companyCode) {\r\n      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号\r\n      const companyInfoMap = {\r\n        \"GZRS\": {\r\n          title: \"广州瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440101MA59UQXX7B\"\r\n        },\r\n        \"HKRS\": {\r\n          title: \"香港瑞旗国际货运代理有限公司\",\r\n          taxNo: \"HK12345678\"\r\n        },\r\n        \"SZRS\": {\r\n          title: \"深圳市瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440300MA5G9UB57Q\"\r\n        },\r\n        \"GZCF\": {\r\n          title: \"广州正泽国际货运代理有限公司\",\r\n          taxNo: \"91440101MA9XRGLH0F\"\r\n        }\r\n      }\r\n\r\n      // 获取对应公司的信息\r\n      const companyInfo = companyInfoMap[companyCode]\r\n\r\n      // 如果找到对应的公司信息，则填充表单\r\n      if (companyInfo) {\r\n        this.formData.richCompanyTitle = companyInfo.title\r\n        this.formData.richVatSerialNo = companyInfo.taxNo\r\n      }\r\n    },\r\n\r\n    // 判断表单项是否禁用\r\n    isDisabled() {\r\n      // 根据以下条件判断表单是否应该禁用：\r\n      // 1. 如果发票状态为已开票\r\n      if (this.type === \"debitNote\" && this.formData.invoiceStatus === \"applied\") {\r\n        return true\r\n      }\r\n\r\n      // 2. 如果报税已锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 3. 如果已支付\r\n      // if (this.formData.actualPayDate) {\r\n      //   return true\r\n      // }\r\n\r\n      // 如果发票已审核，则不可编辑\r\n      if (this.formData.auditStatus === \"审核通过\") {\r\n        return true\r\n      }\r\n\r\n      // 如果没有禁用条件，则表单可编辑\r\n      return false\r\n    },\r\n    handleRichBankCodeChange(row) {\r\n      this.formData.richBankFullname = row.bankBranchName\r\n      this.formData.richBankAccount = row.bankAccount\r\n    },\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"confirmed\": \"已审核\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n    // 申请开票\r\n    async handleApplyInvoice() {\r\n      try {\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新发票状态为已申请\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"applied\",\r\n          applyStuffId: currentUser.sid, // 开票人ID\r\n          appliedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票状态\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"applied\"\r\n          this.formData.applyStuffId = currentUser.sid\r\n          this.formData.appliedTime = currentTime\r\n\r\n          this.$message.success(\"申请开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"applyInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"申请开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"申请开票失败:\", error)\r\n        this.$message.error(\"申请开票失败，请重试\")\r\n      }\r\n    },\r\n    // 信息审核\r\n    async handleAuditInvoice() {\r\n      // 获取当前用户信息\r\n      const currentUser = this.$store.state.user\r\n      const currentTime = new Date().toISOString()\r\n\r\n      // 更新审核信息\r\n      this.formData = {\r\n        ...this.formData,\r\n        invoiceStatus: \"confirmed\",\r\n        auditStuffId: currentUser.sid, // 审核人ID\r\n        auditTime: currentTime, // 审核时间\r\n        auditStatus: \"pass\" // 审核状态\r\n      }\r\n\r\n      // 通知父组件数据变化\r\n      this.$emit(\"auditInvoice\", this.formData)\r\n    },\r\n    // 发送开票\r\n    async handleSendInvoice() {\r\n      try {\r\n        // 检查审核状态，只有审核通过的发票才能开票\r\n        if (this.formData.auditStatus !== \"pass\") {\r\n          this.$message.error(\"只有审核通过的发票才能进行开票操作\")\r\n          return\r\n        }\r\n\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新开票信息\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"issued\", // 发票状态为已开票\r\n          issuedStuffId: currentUser.sid, // 开票人ID\r\n          issuedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票开票信息\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"issued\"\r\n          this.formData.issuedStuffId = currentUser.sid\r\n          this.formData.issuedTime = currentTime\r\n\r\n          this.$message.success(\"发送开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"sendInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"发送开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发送开票失败:\", error)\r\n        this.$message.error(\"发送开票失败，请重试\")\r\n      }\r\n    }\r\n    ,\r\n    // 判断是否可以申请开票\r\n    canApplyInvoice() {\r\n      // 检查是否有发票ID\r\n      if (!this.formData.invoiceId) {\r\n        return false\r\n      }\r\n\r\n      // 检查发票状态\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return false // 已开票不能再次申请\r\n      }\r\n\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        return false // 已申请不能重复申请\r\n      }\r\n\r\n      // 检查是否已报税锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return false\r\n      }\r\n\r\n      // 检查是否已支付（如果需要的话）\r\n      // if (this.formData.actualPayDate) {\r\n      //   return false\r\n      // }\r\n\r\n      return true\r\n    }\r\n    ,\r\n    // 获取申请信息\r\n    getApplyInfo() {\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        // 如果已申请开票，显示申请人+时间\r\n        const currentUser = this.$store.state.user\r\n        let applyTime = \"\"\r\n\r\n        if (this.formData.issuedTime) {\r\n          try {\r\n            applyTime = new Date(this.formData.issuedTime).toLocaleString(\"zh-CN\", {\r\n              year: \"numeric\",\r\n              month: \"2-digit\",\r\n              day: \"2-digit\",\r\n              hour: \"2-digit\",\r\n              minute: \"2-digit\"\r\n            })\r\n          } catch (e) {\r\n            applyTime = \"时间格式错误\"\r\n          }\r\n        }\r\n\r\n        const userName = this.getName(currentUser.sid) || \"未知用户\"\r\n        return `${userName}${applyTime}`\r\n      }\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return \"已开票\"\r\n      }\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return \"已报税锁定\"\r\n      }\r\n      if (this.formData.actualPayDate) {\r\n        return \"已支付\"\r\n      }\r\n      return \"未申请\"\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        } else {\r\n          return \"\"\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    }\r\n    ,\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n      }\r\n    }\r\n    ,\r\n\r\n    // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n    collectRctNumbers() {\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        return\r\n      }\r\n\r\n      // 收集所有不重复的rct号\r\n      const rctNumbers = new Set()\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        if (charge.sqdRctNo && charge.sqdRctNo.trim()) {\r\n          rctNumbers.add(charge.sqdRctNo.trim())\r\n        }\r\n      })\r\n\r\n      // 将rct号转换为数组并排序，然后以逗号分隔\r\n      const rctArray = Array.from(rctNumbers).sort()\r\n      this.formData.relatedOrderNo = rctArray.join(\",\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.yellow-bg {\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.disable-form {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.uploaded-file-preview {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-title {\r\n  font-weight: bold;\r\n  color: #495057;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-links {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-links .el-link {\r\n  font-size: 12px;\r\n}\r\n\r\n/* treeselect组件样式 */\r\n:deep(.vue-treeselect__menu) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n:deep(.vue-treeselect__menu-container) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__dropdown) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 确保下拉框在表格之上 */\r\n:deep(.vue-treeselect__menu-arrow) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__list) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 金额公式样式 */\r\n.amount-formula {\r\n  margin-top: 5px;\r\n  padding: 5px 8px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.amount-formula small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.text-muted {\r\n  color: #909399 !important;\r\n}\r\n</style>\r\n"]}]}